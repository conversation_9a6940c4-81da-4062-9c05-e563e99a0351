import os
import requests

# 模型名称
model = "bus:snap:orngcresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted:user:swe-main-run$harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget_commentary_cs_cross_msg_msc_tools_v2"

# API endpoint
url = "https://openai-proxy.int.prod-southcentralus-hpe-3.dev.openai.org/v1/chat/completions"

# 从环境变量里读取 cookie
orange_cookie = os.getenv("ORANGE_API_COOKIE")

headers = {
    "Content-Type": "application/json",
    "Cookie": orange_cookie,
}

payload = {
    "model": model,
    "messages": [
        {"role": "system", "content": "You are an AI assistant."},
        {"role": "user", "content": "hi"},
    ],
    "temperature": 0,
    "top_p": 0.95,
    "frequency_penalty": 0,
    "presence_penalty": 0,
    "parallel_tool_calls": False,
}

response = requests.post(url, headers=headers, json=payload, verify=False)

print("Status:", response.status_code)
print("Response:", response.json())
